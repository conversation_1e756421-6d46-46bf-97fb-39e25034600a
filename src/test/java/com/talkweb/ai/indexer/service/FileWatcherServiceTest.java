package com.talkweb.ai.indexer.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

class FileWatcherServiceTest {

    @TempDir
    Path tempDir;

    private FileWatcherService fileWatcherService;

    @BeforeEach
    void setUp() throws IOException {
        fileWatcherService = new FileWatcherService(tempDir);
    }

    @AfterEach
    void tearDown() {
        if (fileWatcherService != null) {
            fileWatcherService.stop();
        }
    }

    @Test
    void testFileCreatedEvent() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> createdFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> {
            createdFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File created event should be received");
        assertEquals(testFile, createdFilePath.get(), "Created file path should match");
    }

    @Test
    void testFileModifiedEvent() throws IOException, InterruptedException {
        // Arrange
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Initial content");

        // Ensure file is fully written and timestamp is stable
        Thread.sleep(100);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Add a small delay to ensure the watcher has established baseline state
        Thread.sleep(100);

        Files.writeString(testFile, "Modified content");

        // Assert
        boolean eventReceived = latch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File modified event should be received within the timeout");
        assertEquals(testFile, modifiedFilePath.get(), "The path of the modified file should be correct");
    }

    @Test
    void testFileDeletedEvent() throws IOException, InterruptedException {
        // Arrange
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> deletedFilePath = new AtomicReference<>();

        fileWatcherService.addFileDeletedListener(path -> {
            deletedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Files.delete(testFile);

        // Assert
        boolean eventReceived = latch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File deleted event should be received within the timeout");
        assertEquals(testFile, deletedFilePath.get(), "The path of the deleted file should be correct");
    }

    @Test
    void testMultipleListeners() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(2);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean allEventsReceived = createdLatch.await(5, TimeUnit.SECONDS);
        assertTrue(allEventsReceived, "All file created events should be received by multiple listeners");
    }

    @Test
    void testStartStopService() throws IOException {
        // Act & Assert - No exception should be thrown
        fileWatcherService.start();
        fileWatcherService.start(); // Starting again should be no-op
        fileWatcherService.stop();
        fileWatcherService.stop(); // Stopping again should be no-op
    }

    @Test
    void testListenerExceptionHandling() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);

        // First listener throws exception
        fileWatcherService.addFileCreatedListener(path -> {
            throw new RuntimeException("Test exception");
        });

        // Second listener should still be called
        fileWatcherService.addFileCreatedListener(path -> {
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "Second listener should be called even if first listener throws exception");
    }

    @Test
    void testFileModifiedEventWithEnhancedStability() throws IOException, InterruptedException {
        // Arrange - Using TestUtils for enhanced stability
        Path testFile = TestUtils.createUniqueTestFile(tempDir, "stable-test", "Initial content");

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Ensure watcher has established baseline state
        TestUtils.ensureFileStability(200);

        // Modify file with enhanced stability
        TestUtils.modifyFileStably(testFile, "Modified content with timestamp: " + System.currentTimeMillis());

        // Assert with enhanced waiting
        boolean eventReceived = TestUtils.waitForCondition(
            () -> latch.getCount() == 0,
            Duration.ofSeconds(15)
        );

        assertTrue(eventReceived, "File modified event should be received within the timeout");
        assertEquals(testFile, modifiedFilePath.get(), "The path of the modified file should be correct");

        // Verify file content was actually changed
        assertTrue(TestUtils.verifyFileContent(testFile, "Modified content with timestamp: " + System.currentTimeMillis()) ||
                   Files.readString(testFile).startsWith("Modified content with timestamp:"),
                   "File should contain the modified content");
    }

    @Test
    void testMultipleFileOperationsStability() throws IOException, InterruptedException {
        // Test multiple file operations in sequence to ensure stability
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch modifiedLatch = new CountDownLatch(1);
        CountDownLatch deletedLatch = new CountDownLatch(1);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> modifiedLatch.countDown());
        fileWatcherService.addFileDeletedListener(path -> deletedLatch.countDown());

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Create file
        Path testFile = TestUtils.createUniqueTestFile(tempDir, "multi-op-test", "Initial content");
        assertTrue(createdLatch.await(10, TimeUnit.SECONDS), "File created event should be received");

        // Modify file
        TestUtils.modifyFileStably(testFile, "Modified content");
        assertTrue(modifiedLatch.await(10, TimeUnit.SECONDS), "File modified event should be received");

        // Delete file
        TestUtils.ensureFileStability();
        Files.delete(testFile);
        assertTrue(deletedLatch.await(10, TimeUnit.SECONDS), "File deleted event should be received");
    }
}
